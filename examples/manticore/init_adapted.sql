-- adapted init for current Man<PERSON>ore (avoid PRIMARY KEY(...) syntax)
-- Create RT tables without explicit id column (Manticore auto-assigns _id)
CREATE TABLE IF NOT EXISTS conversations_rt (
  message_id BIGINT,
  turn_id BIGINT,
  topic_id BIGINT,
  role TEXT,
  content TEXT,
  created_at TIMESTAMP,
  summary TEXT,
  -- keep vector column if runtime supports embeddings/float_vector
  summary_vector FLOAT_VECTOR,
  summary_version INT,
  deleted INT DEFAULT 0
) ENGINE=rt
SETTINGS rt_mem_limit=512M, knn_dims=384, knn_type='hnsw';

CREATE TABLE IF NOT EXISTS documents_rt (
  doc_id BIGINT,
  chunk_id BIGINT,
  chunk_text TEXT,
  chunk_vector FLOAT_VECTOR,
  topic_ids STRING,
  indexed_at TIMESTAMP
) ENGINE=rt
SETTINGS rt_mem_limit=1G, knn_dims=384, knn_type='hnsw';