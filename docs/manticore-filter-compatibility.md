# Manticore JSON filter 兼容性诊断与修复建议

概述
- 问题：在不同 Manticore 部署/版本中，JSON `/json/search` 的 filter 写法行为不一致，导致在某些实例上按属性过滤（例如 `topic_id`）返回空结果，进而出现前端“发送消息后无变化”的现象。
- 本次工作：在本地运行多种 `/json/search` payload 实验、修复后端兼容性逻辑并实现“启动探测 + 适配”机制。关键改动位于：
  - [`src/services/conversation_service.js`](src/services/conversation_service.js:27)
  - [`src/services/retrieval_service.js`](src/services/retrieval_service.js:43)
  - [`src/lib/manticore_http_client.js`](src/lib/manticore_http_client.js:1)
  - 启动探测挂载点：[`src/index.js`](src/index.js:99)

实验要点（原始试验汇总）
- 我对下列 JSON payload 进行了系统化测试（针对 `conversations_rt` 表、`topic_id=2` 为例）并记录原始响应：
  1) match_all + bool.must equals
  2) match_all + bool.must in array
  3) bool.filter with equals
  4) query.match + bool.must equals
  5) top-level filter as array
  6) top-level filter as single value
  7) match exact text + bool.must equals

- 主要观察：
  - 文本搜索（按 content）总能找到刚插入的消息，说明写入成功、索引存在。
  - 在本地 Manticore (13.x + Buddy 插件) 环境中，以下写法是可靠的：
    - match_all + bool.must equals (示例 1)
    - match_all + bool.must in array (示例 2)
    - bool.filter with equals (示例 3)
    - top-level filter as array 在某些请求格式下也能正常工作（示例 5）
  - 某些写法（例如 query.match + 嵌套 bool.must，或 top-level filter 单值）在本实例返回空结果或不稳定（示例 4/6），因此应避免作为唯一实现方案。

根本原因
- 不同 Manticore 版本与插件组合对 JSON API 的解析器/路由存在差异；Buddy 插件或其它扩展可能影响 `/json/search` 的 filter 接受方式。
- /sql 接口在部分环境对某些语法会返回 P02 语法错误，因此不能作为通用在线 DDL 或 ad-hoc 查询的唯一手段。

已实施的兼容性改动（代码位置）
- 后端插入：在 [`src/services/conversation_service.js`](src/services/conversation_service.js:27) 中同时写入 `id` 与 `message_id`，避免主键冲突导致 409 错误。
- 检索层：
  - 在 [`src/services/retrieval_service.js`](src/services/retrieval_service.js:43) 的无 query 分支，已实现：
    - 优先调用客户端的探测 API（`detectFilterMode()`）来决定使用哪种 JSON filter 写法；
    - 若探测显示支持 `top_level_array`，则发送 `{ filter: { topic_id: [X] } }`；
    - 否则使用更兼容的 `match_all + bool.must [{ equals: { topic_id: X } }]`；
    - 若两者都失败，则回退到 SQL 查询（`sqlQuery`）以读取最近消息（谨慎使用，仅在能正确响应时）。
- Manticore 客户端：
  - 在 [`src/lib/manticore_http_client.js`](src/lib/manticore_http_client.js:1) 中新增 `detectFilterMode()` 与 `getFilterMode()` 接口，支持运行时探测并缓存当前实例可接受的 filter 写法。
- 启动探测：
  - 在应用入口 [`src/index.js`](src/index.js:99) 中已集成启动时一次性调用 `detectFilterMode()` 的逻辑（避免每次请求触发探测）。

推荐的长期稳健策略（强烈建议采纳）
1. 启动探测 + 适配（已实现）
   - 在应用启动时探测最兼容的 JSON filter 写法并缓存结果（当前实现已加入，建议在生产环境也启用）。
2. 在 CI / 集成环境中加入 Manticore 兼容性测试
   - 在 CI 中启动目标 Manticore 版本的容器，执行一组标准化 CRUD + `/json/search` payload 测试，确保部署版本与应用代码兼容。
3. 记录并规范部署目标版本
   - 明确生产环境应使用的 Manticore 版本与配置（例如 Buddy 插件是否启用、RT 功能完整性等），将其写入项目 README 或部署说明。
4. 避免依赖 /sql 做在线 DDL
   - /sql 在某些构建或版本上可能限制语法，尽量通过容器 init SQL 或在维护窗口执行迁移步骤。
5. 将探测/适配逻辑封装为库
   - 已在 `src/lib/manticore_http_client.js` 添加 detect 接口，建议将其完善为更通用的适配层，并增加单元测试，方便在不同环境间复用。
6. 前端 UX 的短期可用性保障（可选）
   - 在前端保留轻量乐观渲染逻辑（已加入 `public/app.js` 的 optimistic append），但不要把它当作后端检索失败时的长期替代方案。

附件（示例 payload）
- 推荐：match_all + bool.must equals
  - {"index":"conversations_rt","query":{"match_all":{},"bool":{"must":[{"equals":{"topic_id":2}}]}},"limit":20}
- 推荐（多值）：match_all + bool.must in
  - {"index":"conversations_rt","query":{"match_all":{},"bool":{"must":[{"in":{"topic_id":[2,3]}}]}},"limit":20}
- 备用：top-level filter array
  - {"index":"conversations_rt","filter":{"topic_id":[2]},"limit":20}
- 不推荐（在某些实例不稳定）：top-level filter single-value
  - {"index":"conversations_rt","filter":{"topic_id":2},"limit":20}

后续交付（我将执行）
- 在 `src/index.js` 已加入启动探测调用；我将：
  - 确认在生产/CI 中把该调用作为必选步骤；
  - 将本次实验记录补充为 `docs/manticore-filter-compatibility.md`（此文件）并在 repo docs 中保留；
  - 如需，我可以进一步把探测逻辑改为在容器环境变量中写入 result（便于运维脚本读取）。

下一步（请从下列选项选择）
- 我实现将 detectFilterMode 的结果在启动时持久化到环境变量或配置文件（便于外部运维系统读取）。
- 我把本次实验的完整原始输出（所有 curl 请求的原始响应）整理并附在 docs 中（更详尽的证据日志）。
- 我把探测逻辑扩展为更多候选写法并加单元测试（更稳健但需要额外开发时间）。

请在你希望的下一步上给出选择（或直接让我继续执行第一个选项：把 detect 结果写成 env/config）。我会据此继续并把修改提交到仓库。
</result>
</attempt_completion>