-- Migration: add missing columns to conversations_rt to align with application payload
-- Run this against your Manticore RT instance (HTTP/CLI) if the table was created manually
-- Note: adjust types if your Manticore build requires different column types.
-- Recommended: stop indexers that might lock the table, run these, then restart services if needed.

-- Add created_at timestamp to record message creation time
ALTER TABLE conversations_rt ADD COLUMN created_at TIMESTAMP;

-- Add summary_version to track summary generation versioning (default 0)
ALTER TABLE conversations_rt ADD COLUMN summary_version INT DEFAULT 0;

-- Add deleted flag for soft-delete semantics (default 0)
ALTER TABLE conversations_rt ADD COLUMN deleted INT DEFAULT 0;

-- If your existing table still uses 'id' as PK and you want to migrate to message_id as PK,
-- perform the necessary steps carefully: create a new table with correct PK, copy data, then rename.
-- That is a more invasive migration and should be performed during a maintenance window.