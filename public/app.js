const apiBase = '';

function el(id) { return document.getElementById(id); }

async function fetchConversation() {
  const topicId = el('topicId').value;
  try {
    const res = await fetch(`${apiBase}/api/topics/${topicId}/conversation?limit=50`);
    const json = await res.json();
    const list = json.results || [];
    const container = el('messages');
    container.innerHTML = '';
    if (list.length === 0) {
      container.innerHTML = '<div class="meta">No messages yet.</div>';
      return;
    }
    list.forEach(m => {
      const d = document.createElement('div');
      d.className = 'message';
      d.innerHTML = `<div class="meta">#${m.id} · ${m.source_type} · score:${m.score}</div><div>${escapeHtml(m.snippet)}</div>`;
      container.appendChild(d);
    });
  } catch (e) {
    el('messages').innerHTML = '<div class="meta">Failed to fetch conversation. See console for details.</div>';
    console.error(e);
  }
}

async function sendMessage() {
  const topicId = el('topicId').value;
  const role = el('role').value;
  const content = el('content').value;
  if (!content.trim()) return alert('请输入内容');
  try {
    const res = await fetch(`${apiBase}/api/topics/${topicId}/messages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ role, content })
    });
    if (!res.ok) {
      const err = await res.json().catch(() => ({}));
      return alert('发送失败: ' + (err.error || res.status));
    }
    // Read created message from response and optimistically append it to the UI,
    // then trigger a fetchConversation to refresh from the server.
    const created = await res.json().catch(() => null);
    el('content').value = '';
    try {
      // Append optimistic message so user sees immediate feedback even if search backend lags.
      if (created) {
        const container = el('messages');
        const d = document.createElement('div');
        d.className = 'message';
        // Use fields normalized by backend: id or message_id, snippet/content and score optional
        const mid = created.id || created.message_id || '';
        const snippet = created.content || created.snippet || '';
        const score = created.score !== undefined ? created.score : '';
        d.innerHTML = `<div class="meta">#${mid} · ${created.source_type || 'conversation'} · score:${score}</div><div>${escapeHtml(snippet)}</div>`;
        // prepend so newest appears first (match existing UI expectations)
        if (container.firstChild) container.insertBefore(d, container.firstChild);
        else container.appendChild(d);
      }
    } catch (e) {
      // ignore UI append errors
      console.error(e);
    }
    await fetchConversation();
  } catch (e) {
    console.error(e);
    alert('请求失败，请查看控制台');
  }
}

async function enqueueRegenerate() {
  const topicId = el('topicId').value;
  try {
    const res = await fetch(`${apiBase}/api/topics/${topicId}/summary/regenerate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    });
    if (!res.ok) {
      const err = await res.json().catch(() => ({}));
      return alert('enqueue failed: ' + (err.error || res.status));
    }
    alert('regenerate 请求已入队');
  } catch (e) {
    console.error(e);
    alert('请求失败，请查看控制台');
  }
}

function escapeHtml(s) {
  if (!s) return '';
  return s.replace(/[&<>"']/g, (c) => {
    switch (c) {
      case '&': return '&';
      case '<': return '<';
      case '>': return '>';
      case '"': return '"';
      case "'": return "'";
      default: return c;
    }
  });
}

el('refresh').addEventListener('click', fetchConversation);
el('send').addEventListener('click', sendMessage);
el('regen').addEventListener('click', enqueueRegenerate);

// Auto-refresh on load
window.addEventListener('load', () => {
  fetchConversation();
});