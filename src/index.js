const express = require('express');
const bodyParser = require('body-parser');
const conversationService = require('./services/conversation_service.js');
const retrievalService = require('./services/retrieval_service.js');
// Initialize OpenTelemetry (no-op if not configured)
const otel = require('./lib/opentelemetry.js');
otel.init();

const app = express();
app.use(bodyParser.json());

// Serve minimal static UI from ./public
const path = require('path');
app.use(express.static(path.join(__dirname, '../public')));

app.post('/api/topics/:topic_id/messages', async (req, res) => {
  const { topic_id } = req.params;
  const { role, content } = req.body;

  try {
    const message = await conversationService.produceMessage(topic_id, { role, content });
    res.status(201).json(message);
  } catch (err) {
    // On unexpected error, return 500 with minimal info
    res.status(500).json({ error: 'failed to persist message' });
  }
});

/**
 * POST /api/topics/:topic_id/summary/regenerate
 * Body: { message_id? , turn_id?, summary_version? , content? }
 * Behavior:
 *  - Enqueue a regenerate request (best-effort). For the MVP we return a placeholder new summary_version
 *    computed as (provided summary_version || 0) + 1 and trigger async processing.
 */
app.post('/api/topics/:topic_id/summary/regenerate', async (req, res) => {
  const { topic_id } = req.params;
  const { message_id = null, turn_id = null, summary_version = 0, content = '' } = req.body || {};

  // compute placeholder new version
  const newVersionPlaceholder = (Number(summary_version) || 0) + 1;

  // fire-and-forget enqueue; swallow errors to avoid failing client
  try {
    // enqueueRegenerateSummary should be implemented in conversation_service to actually enqueue/process
    conversationService.enqueueRegenerateSummary(Number(topic_id), { message_id, turn_id, content, summary_version: Number(summary_version) }).catch(() => {});
  } catch (err) {
    // ignore enqueue errors for API response (endpoint is best-effort)
  }

  // Accepted for async processing, return summary_version placeholder
  res.status(202).json({ ok: true, summary_version: newVersionPlaceholder, message: 'regenerate enqueued' });
});

/**
 * GET /api/topics/:topic_id/conversation
 * Query params:
 *  - cursor (optional)
 *  - limit (optional)
 *  - q (optional): fulltext query to filter conversation
 *
 * Uses retrieval_service.searchContext under the hood to return conversation-like results.
 */
app.get('/api/topics/:topic_id/conversation', async (req, res) => {
  const { topic_id } = req.params;
  const { cursor = null, limit = 20, q = null } = req.query;

  try {
    // Primary: use searchContext to fetch candidates (hybrid search).
    const result = await retrievalService.searchContext({ topic_id: Number(topic_id), query: q, top_k: Number(limit) });

    // Fallback: if searchContext returned no results (due to filter incompatibility),
    // query the RT table directly via Manticore SQL endpoint to retrieve recent messages.
    if ((!result || !result.results || result.results.length === 0) && typeof require('./lib/manticore_http_client').sqlQuery === 'function') {
      try {
        const manticore = require('./lib/manticore_http_client');
        // Use SQL to select recent rows for the topic. Note: created_at might be stored as timestamp number.
        const sql = `SELECT * FROM conversations_rt WHERE topic_id=${Number(topic_id)} ORDER BY created_at DESC LIMIT ${Number(limit)}`;
        const sqlRes = await manticore.sqlQuery(sql);
        // sqlRes shape may vary; try to extract rows robustly
        let rows = [];
        if (Array.isArray(sqlRes) && sqlRes.length > 0 && Array.isArray(sqlRes[0].rows)) {
          rows = sqlRes[0].rows;
        } else if (sqlRes && Array.isArray(sqlRes.rows)) {
          rows = sqlRes.rows;
        } else if (Array.isArray(sqlRes)) {
          // some manticore versions return array-of-objects rows directly
          rows = sqlRes;
        }
        const results = (rows || []).map(r => ({
          id: r.id || r.message_id || null,
          score: null,
          source_type: 'conversation',
          snippet: r.content || ''
        }));
        return res.status(200).json({ ok: true, cursor: null, results, meta: { knn: null, confidence: 0 } });
      } catch (err) {
        // swallow and return whatever searchContext returned (possibly empty)
      }
    }

    res.status(200).json({ ok: true, cursor: null, results: result.results || [], meta: result.meta || {} });
  } catch (err) {
    res.status(500).json({ error: 'failed to fetch conversation' });
  }
});

/**
 * GET /api/topics/:topic_id/summary
 * Returns recent summaries for a topic (uses retrieval_service to fetch by topic_id and summary presence).
 * Query params:
 *  - limit (optional)
 */
app.get('/api/topics/:topic_id/summary', async (req, res) => {
  const { topic_id } = req.params;
  const { limit = 20 } = req.query;

  try {
    // Query for summaries via searchContext using a match for 'summary' presence — for MVP we use same searchContext.
    const result = await retrievalService.searchContext({ topic_id: Number(topic_id), query: null, top_k: Number(limit) }, { /* options */ });
    // Filter results to include summary snippets if available in snippet (the retrieval service normalizes snippet)
    res.status(200).json({ ok: true, results: result.results || [], meta: result.meta || {} });
  } catch (err) {
    res.status(500).json({ error: 'failed to fetch summaries' });
  }
});

module.exports = app;

// If executed directly, start HTTP server for quick local testing
if (require.main === module) {
  const PORT = process.env.PORT || 3000;

  // Runtime detect Manticore JSON filter compatibility and cache it before serving.
  // This avoids per-request detection overhead and ensures retrieval_service uses the
  // correct JSON payload form for filtering by topic_id.
  (async () => {
    try {
      const manticore = require('./lib/manticore_http_client');
      if (typeof manticore.detectFilterMode === 'function') {
        // perform detection once on startup
        const mode = await manticore.detectFilterMode();
        // eslint-disable-next-line no-console
        console.log('Manticore filter detection result:', mode);
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      console.warn('Manticore filter detection failed on startup', err && err.message ? err.message : err);
    } finally {
      app.listen(PORT, () => {
        // eslint-disable-next-line no-console
        console.log(`Server listening on http://localhost:${PORT}`);
      });
    }
  })();
}