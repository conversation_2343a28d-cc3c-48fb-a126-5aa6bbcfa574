/*
src/services/retrieval_service.js

Retrieval Service - hybrid fulltext + vector search helper

Exports:
- async function searchContext({ topic_id, query, top_k = 10, mode = 'hybrid' }, options = {})
  - options may include: vector (embedding array), knnField, filters, client (injected manticore client)

Behavior:
- If vector provided and no query -> knnSearch
- If query provided and no vector -> match search
- If both provided and mode === 'hybrid' -> knn first to get candidate ids, then match constrained by ids
- Returns standardized result: { results: [{ id, score, source_type, snippet }], meta: { knn, confidence } }
*/

const manticoreClient = require('../lib/manticore_http_client');
const { knnSearch, hybridSearch } = manticoreClient;

function normalizeHits(manticoreHits) {
  // manticoreHits expected shape: { hits: { hits: [ { _id, _score, _source } ], total } }
  const hits = (manticoreHits && manticoreHits.hits && manticoreHits.hits.hits) || [];
  return hits.map(h => ({
    id: h._id,
    score: h._score || null,
    source_type: (h._source && h._source.source_type) || 'conversation',
    snippet: (h._source && h._source.content) || (h._source && h._source.chunk_text) || ''
  }));
}

/**
 * confidence heuristic: based on presence of scores and number of hits
 */
function computeConfidence(hits) {
  if (!hits || hits.length === 0) return 0.0;
  const scores = hits.map(h => h.score).filter(s => typeof s === 'number');
  if (scores.length === 0) return 0.5;
  const avg = scores.reduce((a, b) => a + b, 0) / scores.length;
  // normalize heuristically
  return Math.max(0, Math.min(1, (avg + 1) / 2));
}

async function searchContext({ topic_id, query = null, top_k = 10, mode = 'hybrid' } = {}, options = {}) {
  // Use the full manticore client by default so we have access to httpClient for raw JSON endpoints.
  const client = options.client || require('../lib/manticore_http_client');
  const knnField = options.knnField || 'summary_vector';
  const vector = options.vector || null;
  const filters = options.filters || (topic_id ? { topic_id } : null);

  // If neither query nor vector provided, but topic_id is present -> return recent messages for the topic.
  // This enables the UI to load conversation history when no explicit query is supplied.
  if (!query && !vector) {
    if (topic_id) {
      // perform a simple match_all constrained by topic_id using bool.must + equals/in,
      // which is compatible across more Manticore JSON API deployments.
      try {
        // If the client supports runtime detection of filter mode, prefer the detected mode.
        // Otherwise default to a conservative match_all + bool.must equals payload.
        let mode = null;
        if (client && typeof client.detectFilterMode === 'function') {
          try {
            mode = await client.detectFilterMode();
          } catch (e) {
            mode = null;
          }
        }

        // Build and send request depending on detected/assumed mode
        try {
          if (mode === 'top_level_array') {
            const payload = { index: 'conversations_rt', filter: { topic_id: [Number(topic_id)] }, limit: top_k };
            const rawRes = await client.httpClient.post('/json/search', payload).then(r => r.data);
            const hits = rawRes && rawRes.hits ? rawRes.hits : rawRes;
            const results = normalizeHits(hits);
            const confidence = computeConfidence(results);
            return { results, meta: { knn: null, confidence } };
          }

          // default / fallback: bool.must equals (most compatible)
          const mustClause = [{ equals: { topic_id: Number(topic_id) } }];
          const payload = {
            index: 'conversations_rt',
            query: {
              match_all: {},
              bool: { must: mustClause }
            },
            limit: top_k
          };
          const rawRes = client.httpClient
            ? await client.httpClient.post('/json/search', payload).then(r => r.data)
            : await client.hybridSearch({ index: 'conversations_rt', matchQuery: "", k: top_k });
          // rawRes is the full response, pass it directly to normalizeHits
          const results = normalizeHits(rawRes);
          const confidence = computeConfidence(results);
          return { results, meta: { knn: null, confidence } };
        } catch (err) {
          // Fallback: try SQL query if available (some deployments may not support JSON DDL/filtering well)
          try {
            if (typeof client.sqlQuery === 'function') {
              const sql = `SELECT * FROM conversations_rt WHERE topic_id=${Number(topic_id)} ORDER BY created_at DESC LIMIT ${Number(top_k)}`;
              const sqlRes = await client.sqlQuery(sql);
              const rows = Array.isArray(sqlRes) && sqlRes.length > 0 && Array.isArray(sqlRes[0].rows)
                ? sqlRes[0].rows
                : (sqlRes && sqlRes.rows) || [];
              const hits = { hits: { hits: rows.map(r => ({ _id: r.id || r.message_id || null, _score: null, _source: r })), total: rows.length } };
              const results2 = normalizeHits(hits);
              const confidence2 = computeConfidence(results2);
              return { results: results2, meta: { knn: null, confidence: confidence2 } };
            }
          } catch (err2) {
            // ignore and fallthrough to empty result
          }
          // on error, return empty but not throw (preserve best-effort behavior)
          return { results: [], meta: { knn: null, confidence: 0 } };
        }
      } catch (err) {
        // Fallback: try SQL query if available (some deployments may not support JSON DDL/filtering well)
        try {
          if (typeof client.sqlQuery === 'function') {
            const sql = `SELECT * FROM conversations_rt WHERE topic_id=${Number(topic_id)} ORDER BY created_at DESC LIMIT ${Number(top_k)}`;
            const sqlRes = await client.sqlQuery(sql);
            const rows = Array.isArray(sqlRes) && sqlRes.length > 0 && Array.isArray(sqlRes[0].rows)
              ? sqlRes[0].rows
              : (sqlRes && sqlRes.rows) || [];
            const hits = { hits: { hits: rows.map(r => ({ _id: r.id || r.message_id || null, _score: null, _source: r })), total: rows.length } };
            const results2 = normalizeHits(hits);
            const confidence2 = computeConfidence(results2);
            return { results: results2, meta: { knn: null, confidence: confidence2 } };
          }
        } catch (err2) {
          // ignore and fallthrough to empty result
        }
        // on error, return empty but not throw (preserve best-effort behavior)
        return { results: [], meta: { knn: null, confidence: 0 } };
      }
    }
    return { results: [], meta: { knn: null, confidence: 0 } };
  }

  // Case 1: both provided and hybrid mode -> prefer knn-first constrained match
  if (query && vector && mode === 'hybrid') {
    // 1) knn to get candidate ids
    const knnRes = await client.knnSearch({ index: 'conversations_rt', field: knnField, vector, k: top_k, ef: options.ef || 64, filters });
    const knnHits = (knnRes && knnRes.hits && knnRes.hits.hits) || [];
    const ids = knnHits.map(h => h._id);
    if (ids.length === 0) {
      return { results: [], meta: { knn: knnRes, confidence: 0 } };
    }
    // 2) constrained match by ids - reuse hybridSearch or search with filter
    const matchRes = await client.hybridSearch({ index: 'conversations_rt', matchQuery: query, knnField, knnVector: vector, k: top_k });
    // hybridSearch in our client returns { hits: <res>, knn: <knnRes> }
    const hits = matchRes && matchRes.hits ? matchRes.hits : matchRes;
    const results = normalizeHits(hits);
    const confidence = computeConfidence(results);
    return { results, meta: { knn: knnRes, confidence } };
  }

  // Case 2: only vector provided -> knn search
  if (vector && !query) {
    const knnRes = await client.knnSearch({ index: 'conversations_rt', field: knnField, vector, k: top_k, ef: options.ef || 64, filters });
    const results = normalizeHits(knnRes);
    const confidence = computeConfidence(results);
    return { results, meta: { knn: knnRes, confidence } };
  }

  // Case 3: only query provided -> match search
  if (query && !vector) {
    const matchRes = await client.hybridSearch({ index: 'conversations_rt', matchQuery: query, k: top_k });
    const hits = matchRes && matchRes.hits ? matchRes.hits : matchRes;
    const results = normalizeHits(hits);
    const confidence = computeConfidence(results);
    return { results, meta: { knn: null, confidence } };
  }

  throw new Error('searchContext requires at least query or vector');
}

module.exports = {
  searchContext,
  normalizeHits,
  computeConfidence
};