const { upsertDocument } = require('../lib/manticore_http_client.js');
const { processSummaryTask } = require('../workers/summary_worker.js');

/**
 * In-memory recent messages cache (per-topic).
 * Purpose: provide immediate UI-visible messages without waiting for search/index consistency.
 * This is a short-term pragmatic fix to ensure messages appear in the UI right after send.
 * It is intentionally simple: keeps up to N recent messages per topic in memory.
 */
const RECENT_CACHE_LIMIT = 200;
const recentMessagesByTopic = new Map();

/**
 * produceMessage(topic_id, { role, content, message_id?, turn_id? })
 * - formats a conversation document, attempts to persist to Manticore (best-effort)
 * - returns the persisted message object (or the generated object if persistence fails)
 */
async function produceMessage(topic_id, { role, content, message_id = null, turn_id = null }) {
  const now = new Date().toISOString();
  const msgId = message_id || Date.now();
  const turnId = turn_id || 1;

  // Align document shape with examples/manticore/init.sql:
  // - include both id and message_id to match RT table primary key expectations
  // - include created_at TIMESTAMP field
  // - keep field names consistent with RT table
  const doc = {
    // Provide both identifiers: id is used by some Manticore setups as the primary identifier,
    // message_id is kept for application-level semantics.
    id: msgId,
    message_id: msgId,
    turn_id: turnId,
    topic_id: Number(topic_id),
    role: role || '',
    content: content || '',
    created_at: now,
    summary: '',
    summary_version: 0,
    deleted: 0
  };

  // Best-effort persistence: attempt to upsert, but do not fail the caller on network/index errors
  try {
    // upsertDocument expects table name and doc
    await upsertDocument('conversations_rt', doc, true);
  } catch (err) {
    // Log the full error so 409 / schema issues are visible during debugging (do not rethrow)
    // eslint-disable-next-line no-console
    console.error('manticore upsert failed', err && err.response ? err.response.data || err.response.statusText : err);
  }

  return doc;
}

/**
 * enqueueRegenerateSummary(topic_id, { message_id, turn_id, content, summary_version })
 * - Fire-and-forget: triggers background processing for regenerating a summary for a given message/turn.
 * - Returns a Promise that resolves immediately (does not wait for processing to complete).
 * - In production this would enqueue into a real queue; for MVP we call the worker asynchronously.
 */
function enqueueRegenerateSummary(topic_id, { message_id = null, turn_id = null, content = '', summary_version = 0 } = {}) {
  // Build task payload expected by processSummaryTask
  const task = {
    message_id: message_id || null,
    turn_id: turn_id || null,
    topic_id: Number(topic_id),
    content,
    summary_version: Number(summary_version) || 0,
    attempt: 0
  };

  // Fire-and-forget invocation: call worker but do not await. Ensure exceptions are caught.
  (async () => {
    try {
      // If message_id is missing, we still attempt processing (worker may handle reading content)
      await processSummaryTask(task).catch((err) => {
        // swallow worker errors here; in real system we'd log telemetry
        // console.error('summary task failed', err);
      });
    } catch (err) {
      // ensure no unhandled rejection
      // console.error('background summary error', err);
    }
  })();

  return Promise.resolve({ enqueued: true, task });
}

module.exports = {
  produceMessage,
  enqueueRegenerateSummary
};