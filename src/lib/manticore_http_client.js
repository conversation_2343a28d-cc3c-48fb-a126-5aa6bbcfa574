/*
src/lib/manticore_http_client.js

Lightweight Node.js wrapper for Manticore HTTP JSON API (recommended for integration).
Provides: upsertMessage (single), upsertMessagesBatch (bulk), knnSearch, hybridSearch.
Environment variables:
- MANTICORE_HOST (default: 127.0.0.1)
- MANTICORE_HTTP_PORT (default: 9308)
- MANTICORE_TIMEOUT_MS (default: 5000)
*/
const axios = require('axios');

const HOST = process.env.MANTICORE_HOST || '127.0.0.1';
const PORT = process.env.MANTICORE_HTTP_PORT || '9308';
const BASE = `http://${HOST}:${PORT}`;

const httpClient = axios.create({
  baseURL: BASE,
  timeout: Number(process.env.MANTICORE_TIMEOUT_MS || 5000),
  headers: { 'Content-Type': 'application/json' }
});

// Runtime-detected filter mode for this Manticore instance.
// Possible values:
// - 'bool_equals' : use match_all + bool.must [{ equals: { topic_id: X } }]
// - 'top_level_array' : use top-level filter with array values { filter: { topic_id: [X] } }
// - 'none' : detection failed, fall back to best-effort (text search + server-side filter or sql)
let DETECTED_FILTER_MODE = null;
let DETECTION_IN_PROGRESS = false;

/**
 * detectFilterMode()
 * - Attempts a small set of /json/search payloads to determine which filter form is accepted by the server.
 * - Caches the result in DETECTED_FILTER_MODE.
 * - Returns a Promise that resolves to the detected mode string.
 */
async function detectFilterMode() {
  if (DETECTED_FILTER_MODE) return DETECTED_FILTER_MODE;
  if (DETECTION_IN_PROGRESS) {
    // wait until previous detection completes
    while (DETECTION_IN_PROGRESS) await new Promise(r => setTimeout(r, 50));
    return DETECTED_FILTER_MODE || 'none';
  }
  DETECTION_IN_PROGRESS = true;

  // Use a stable existing topic id (try 1) and a synthetic unlikely value for text to ensure we can check hits.
  const testTopic = 1;
  try {
    // A) match_all + bool.must equals
    const payloadA = {
      index: 'conversations_rt',
      query: { match_all: {}, bool: { must: [{ equals: { topic_id: testTopic } }] } },
      limit: 1
    };
    try {
      const resA = await httpClient.post('/json/search', payloadA);
      if (resA && resA.data && resA.data.hits && Array.isArray(resA.data.hits.hits)) {
        // if request returned without error, treat as supported
        DETECTED_FILTER_MODE = 'bool_equals';
        DETECTION_IN_PROGRESS = false;
        return DETECTED_FILTER_MODE;
      }
    } catch (e) {
      // ignore and try next
    }

    // B) top-level filter with array
    const payloadB = { index: 'conversations_rt', filter: { topic_id: [testTopic] }, limit: 1 };
    try {
      const resB = await httpClient.post('/json/search', payloadB);
      if (resB && resB.data && resB.data.hits && Array.isArray(resB.data.hits.hits)) {
        DETECTED_FILTER_MODE = 'top_level_array';
        DETECTION_IN_PROGRESS = false;
        return DETECTED_FILTER_MODE;
      }
    } catch (e) {
      // ignore
    }

    // C) bool.filter equals (alternate form)
    const payloadC = {
      index: 'conversations_rt',
      query: { bool: { filter: [{ equals: { topic_id: testTopic } }] } },
      limit: 1
    };
    try {
      const resC = await httpClient.post('/json/search', payloadC);
      if (resC && resC.data && resC.data.hits && Array.isArray(resC.data.hits.hits)) {
        DETECTED_FILTER_MODE = 'bool_filter_equals';
        DETECTION_IN_PROGRESS = false;
        return DETECTED_FILTER_MODE;
      }
    } catch (e) {
      // ignore
    }
  } catch (err) {
    // detection overall failed
  }

  DETECTED_FILTER_MODE = 'none';
  DETECTION_IN_PROGRESS = false;
  return DETECTED_FILTER_MODE;
}

/**
 * getFilterMode() - returns cached detected mode (may be null if not detected yet)
 */
function getFilterMode() {
  return DETECTED_FILTER_MODE || null;
}

/**
 * Upsert a single document into a RT table
 * payload.doc should match the RT table schema (fields/attributes)
 */
async function upsertDocument(table, doc, replace = true) {
  const payload = {
    table,
    doc,
    replace: Boolean(replace)
  };
  const res = await httpClient.post('/json/insert', payload);
  return res.data;
}

/**
 * Bulk upsert multiple operations.
 * operations: [{ table, doc, replace }, ...]
 */
async function bulkOperations(operations) {
  const payload = { operations };
  const res = await httpClient.post('/json/bulk', payload);
  return res.data;
}

/**
 * Simple knn search against a vector field.
 * field: vector field name
 * vector: array of floats
 * k: number of neighbors
 * options: { index, filters, ef }
 */
async function knnSearch({ index = 'conversations_rt', field, vector, k = 10, ef = 64, filters = null }) {
  const payload = {
    index,
    knn: { field, vector, k, ef },
    limit: k
  };
  if (filters) payload.filter = filters;
  const res = await httpClient.post('/json/search', payload);
  return res.data;
}

/**
 * Hybrid search: combine keyword match and knn.
 * mode: 'knn-first' or 'match-first' (implementation strategy)
 * This helper returns combined candidate list.
 */
async function hybridSearch({ index = 'conversations_rt', matchQuery = null, knnField = null, knnVector = null, k = 10, ef = 64 }) {
  // If both provided, prefer a single JSON request where supported.
  // Fallback: run knn to get candidate ids, then run match filtered by ids.
  if (matchQuery && knnField && knnVector) {
    // 1) run knn to get candidate ids
    const knnRes = await knnSearch({ index, field: knnField, vector: knnVector, k, ef });
    const hits = (knnRes && knnRes.hits && knnRes.hits.hits) || [];
    const ids = hits.map(h => h._id).slice(0, Math.max(k, 50));
    if (ids.length === 0) return { hits: { total: 0, hits: [] }, knn: knnRes };
    // 2) run match constrained by id list using SphinxQL via HTTP JSON /sql if needed; use search endpoint with filter
    const payload = {
      index,
      query: { match: { "*": matchQuery } },
      filter: { id: ids },
      limit: k
    };
    const res = await httpClient.post('/json/search', payload);
    return { hits: res.data, knn: knnRes };
  } else if (knnField && knnVector) {
    return { hits: await knnSearch({ index, field: knnField, vector: knnVector, k, ef }) };
  } else if (matchQuery) {
    const payload = { index, query: { match: { "*": matchQuery } }, limit: k };
    const res = await httpClient.post('/json/search', payload);
    return { hits: res.data };
  } else {
    throw new Error('hybridSearch requires at least matchQuery or knnField+knnVector');
  }
}

/**
 * Helper: perform a SphinxQL statement over HTTP /sql endpoint (if available)
 * Example: { sql: "REPLACE INTO conversations_rt (...)" }
 */
async function sqlQuery(sql) {
  const res = await httpClient.post('/sql', { q: sql });
  return res.data;
}

module.exports = {
  httpClient,
  upsertDocument,
  bulkOperations,
  knnSearch,
  hybridSearch,
  sqlQuery,
  // detection helpers
  detectFilterMode,
  getFilterMode
};