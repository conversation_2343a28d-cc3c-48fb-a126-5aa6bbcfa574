# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
.pnpm-debug.log

# Project local configs & sensitive data
.roo/
.vibedev/

# Local/tooling files to ignore
.augment/
.babelrc
RELEASE_NOTES.md

# Env
.env
.env.local
.env.*.local

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Editor
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# Docker
docker-compose.override.yml
**/docker-compose.override.yml

# Build
dist/
build/
coverage/
.nyc_output/

# Misc
*.pem
tmp/
.local/